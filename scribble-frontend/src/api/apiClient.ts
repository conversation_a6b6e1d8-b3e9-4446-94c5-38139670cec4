import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import { Alert, Platform } from "react-native";
import { navigate, navigationRef } from "@/App";
// import { BASE_URL } from "@env";
import { getAccessToken } from "../screens/login/api";
import { useLogoutUser } from "../hooks/useLogoutUser";
import { clearSecureStorage, getSecureItem, removeSecureItem, setSecureItem } from "../utils/cryptoHelper";
import { getDeviceId, getDeviceName, getDeviceTimeZone, getIPAddress, getOsVersion } from "../utils/deviceDetails";



// const BASE_URL: string = "https://api-dev.goscribble.ai/api/v1/";


// Developemnt
//const BASE_URL: string = "https://api-dev.goscribble.ai/api/";

// Stagging 
//const BASE_URL: string = "https://api-stg.goscribble.ai/api/";
//Production
const BASE_URL: string = "https://api-stg.goscribble.ai/api/";


// console.log("BASE_URL--->",BASE_URL)

const PAGE_EXPIRED_STATUS_CODE = 419;
const AUTHENTICATION_ERROR_STATUS_CODE = 401;
const INVALID_TOKEN = 403;

// Global flags to prevent multiple alerts and handlers
let isAlertShowing = false;
let isHandling401 = false;
let alertTimeoutId: ReturnType<typeof setTimeout> | null = null;

// Cross-platform alert implementation that guarantees only one alert at a time
const showUniqueAlert = (title: string, message: string, callback?: () => void): void => {
  // If alert is already showing, don't show another one
  if (isAlertShowing) return;

  // Set flag to prevent multiple alerts
  isAlertShowing = true;

  // Clear any existing timeout
  if (alertTimeoutId) {
    clearTimeout(alertTimeoutId);
    alertTimeoutId = null;
  }

  // Safety timeout to reset flag after 10 seconds in case something goes wrong
  alertTimeoutId = setTimeout(() => {
    isAlertShowing = false;
  }, 10000);

  if (Platform.OS === 'web') {
    // Web implementation using browser alert
    try {
      const confirmed = window.confirm(`${title}\n\n${message}`);

      // Clear timeout and reset flag
      if (alertTimeoutId) {
        clearTimeout(alertTimeoutId);
        alertTimeoutId = null;
      }
      isAlertShowing = false;

      // Execute callback if user confirmed
      if (callback && confirmed) {
        callback();
      }
    } catch (e) {
      console.error("Error showing web alert:", e);
      isAlertShowing = false;
    }
  } else {
    // Native implementation using React Native Alert
    Alert.alert(
      title,
      message,
      [{
        text: 'OK',
        onPress: () => {
          // Clear timeout and reset flag
          if (alertTimeoutId) {
            clearTimeout(alertTimeoutId);
            alertTimeoutId = null;
          }
          isAlertShowing = false;

          // Execute callback
          if (callback) callback();
        }
      }]
    );
  }
};

// Helper function to navigate to login screen 
const navigateToLogin = async (): Promise<void> => {
  // console.log("Inside navigate")

  // await logOutUser();
  const orgName = await getSecureItem("org")
  //const storedRecording = await getSecureItem("recording");

  console.log("org Name :", orgName)
  await clearSecureStorage();
  if (orgName !== null) {
    await setSecureItem("org", orgName);
  }
  // if(storedRecording !==null){
  //   await setSecureItem("recording", storedRecording);
  // }
  if (Platform.OS === 'web') {
    // await logOutUser();
    window.location.href = '/LoginTenant';
  } else {
    console.log("Going to login ")
    navigate("LoginTenant")
    // navigation.replace("LoginTenant")
  }
};

// Centralized 401 handler to prevent duplicate processing
const handle419Error = (): void => {
  // If already handling a 401 error, don't do it again
  if (isHandling401) return;

  // Set flag to prevent duplicate handling
  isHandling401 = true;

  try {
    // Remove auth token

    // Show alert and navigate to login on user confirmation
    showUniqueAlert(
      'Session Expired',
      'Your session has expired. Please log in again.',
      () => {
        console.log("Navigating to login ")
        navigateToLogin();
        // Reset 401 handling flag after navigation
        setTimeout(() => {
          isHandling401 = false;
        }, 1000);
      }
    );
  } catch (e) {
    console.error("Error handling 401:", e);
    // Reset flag after error
    setTimeout(() => {
      isHandling401 = false;
    }, 1000);

    // If there's an error with AsyncStorage or alert, try to navigate anyway
    navigateToLogin();
  }
};

export const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 30000,
});

// Add a request interceptor to attach the token
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig): Promise<AxiosRequestConfig> => {
    try {
      // console.log(config.baseURL)
      // const token = await AsyncStorage.getItem("authToken");
      const token = await getSecureItem("authToken")
      // let names = window.location.hostname.split(".")
      //  let names = ["dev1"]
      let names = await getSecureItem("org");
      // let names =  await AsyncStorage.getItem("org");;


      if (config.headers) {
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        if (names && names !== "") {
          config.headers["x-tenant-id"] = names
        }
        if (Platform.OS !== "web") {

          config.headers["x-device-id"] = await getDeviceId()
          config.headers["x-device-model"] = getDeviceName()
          config.headers["x-platform"] = Platform.OS
          config.headers["x-os-version"] = getOsVersion()
          config.headers["x-ip-address"] = await getIPAddress()
          config.headers["x-app-version"] = "1.0.0"
          config.headers["x-timezone"] = getDeviceTimeZone()
        }
      }
      console.log("config-->",JSON.stringify(config))
      return config;
    } catch (error) {
      console.error("Error in request interceptor:", error);
      return config;
    }
  },
  (error: any): Promise<never> => {
    return Promise.reject(error);
  }
);


apiClient.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => response,
  async (error) => {
    // console.log("error :",JSON.stringify(error));
    // Mark this error as handled by interceptor
    (error as any)._handledByInterceptor = true;

    const originalRequest = error.config;
    //console.log("Error Status Code :", error.response?.status)
    console.log("Error Details:",JSON.stringify(error.response?.data))
    if (error.response && error .response.status === PAGE_EXPIRED_STATUS_CODE) {

      handle419Error();
      // } else if (error.response?.status === AUTHENTICATION_ERROR_STATUS_CODE) {
    } else if (error.response?.status === AUTHENTICATION_ERROR_STATUS_CODE && !originalRequest?._retry) {
      // await handle419Error();
      // console.log("\n\nError : ", JSON.stringify(error.response.data.errors[0].code))
      // console.log("status error")

      // console.log("\n\nError Details : ", JSON.stringify(error.response))
      let errorType = error.response.data.errorCode;
     // console.log("errorType:",errorType)
      if (errorType !== "WRONG_CREDENTIALS") {
        //  handle419Error();

        originalRequest._retry = true;
        try {
          await removeSecureItem('authToken')

          const refreshToken = await getSecureItem('refreshToken');
          // const res = await apiClient.post('auth/refresh', {
          //   refreshToken: refreshToken,
          // });
          const resp = await getAccessToken(refreshToken as string)

          if (resp.status == "ok") {
            const newAccessToken = resp.data.accessToken;
            await setSecureItem('authToken', newAccessToken);

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${ newAccessToken }`;
            return apiClient(originalRequest);
          } else {
            // navigateToLogin()
            handle419Error()
            return Promise.reject(new Error("Refresh Token Error"));
          }
        } catch (refreshError) {
          // Refresh failed – logout user
          // navigateToLogin()
          handle419Error()
          return Promise.reject(refreshError);
        }
      }
    } else if (!error.response) {
      // Network error
      showUniqueAlert('Connection Error', 'Please check your internet connection and try again.');
    } else if (error.response?.status === INVALID_TOKEN) {

      handle419Error()
    } else if (error.response.status >= 500) {
      //console.log("Server Error : ",JSON.stringify(error.response))
      return Promise.reject(error.response.data);
      // Server error
      // showUniqueAlert('Server Error', 'We\'re experiencing technical difficulties. Please try again later.');
    }

    return Promise.reject(error.response.data);
  }
);


export const uploadApiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "multipart/form-data",
  },
  timeout: 60000,
});

// Add a request interceptor to attach the token
uploadApiClient.interceptors.request.use(
  async (config: AxiosRequestConfig): Promise<AxiosRequestConfig> => {
    try {
      // const token = await AsyncStorage.getItem("authToken");
      //  let names = window.location.hostname.split(".")
      const token = await getSecureItem("authToken")
      let names = await getSecureItem("org");
      //  let names = await AsyncStorage.getItem("org")

      if (config.headers) {
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        if (names && names !== "") {
          config.headers["x-tenant-id"] = names
        }
      }
      // if (token && config.headers) {
      //   config.headers.Authorization = `Bearer ${token}`;
      // }
      return config;
    } catch (error) {
      return config;
    }
  },
  (error: any): Promise<never> => {
    return Promise.reject(error);
  }
);

// Add a response interceptor for error handling
uploadApiClient.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => response,
  async (error: AxiosError): Promise<never> => {
    // Mark this error as handled by interceptor
    (error as any)._handledByInterceptor = true;

    if (error.response && error.response.status === PAGE_EXPIRED_STATUS_CODE) {
      await handle419Error();
    }
    return Promise.reject(error);
  }
);


