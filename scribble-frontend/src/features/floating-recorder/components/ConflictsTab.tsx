import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

// M-GG Flagging interfaces
interface FlaggingPair {
  M_Item: string;
  GG_item: string;
  isDiscrepant: boolean;
  notes: string;
  M_Question?: string;
  GG_Question?: string;
  M_Answer?: string;
  GG_Answer?: string;
}

interface FlaggingRule {
  [key: string]: FlaggingPair[];
}

// Hard-coded sample data for POC with realistic question text
const sampleFlaggingData: FlaggingRule = {
  "7_rule_partial_independence": [
    {
      M_Item: "M1810",
      GG_item: "GG0130.A",
      isDiscrepant: false,
      notes: "M=0, GG=6 - Grooming ≈ oral hygiene - Compliant",
      M_Question: "Grooming: Current ability to tend safely to personal hygiene needs",
      GG_Question: "Eating: Current ability to use suitable utensils to bring food to mouth",
      M_Answer: "0 - Independent",
      GG_Answer: "6 - Independent"
    },
    {
      M_Item: "M1810",
      GG_item: "GG0130.B",
      isDiscrepant: false,
      notes: "M=0, GG=6 - Grooming ≈ oral hygiene - Compliant",
      M_Question: "Grooming: Current ability to tend safely to personal hygiene needs",
      GG_Question: "Oral hygiene: Current ability to use suitable items to clean teeth",
      M_Answer: "0 - Independent",
      GG_Answer: "6 - Independent"
    },
    {
      M_Item: "M1820",
      GG_item: "GG0130.C",
      isDiscrepant: true,
      notes: "M=2, GG=1 - Upper dressing - Expected GG in [2,3,4] but got 1",
      M_Question: "Dressing Upper Body: Current ability to dress and undress above the waist",
      GG_Question: "Toileting hygiene: Current ability to maintain perineal hygiene",
      M_Answer: "2 - Minimal assistance",
      GG_Answer: "1 - Setup or clean-up assistance"
    },
    {
      M_Item: "M1830",
      GG_item: "GG0130.E",
      isDiscrepant: false,
      notes: "M=1, GG=5 - Bathing - Compliant",
      M_Question: "Bathing: Current ability to wash entire body safely",
      GG_Question: "Shower/bathe self: Current ability to wash entire body in shower or tub",
      M_Answer: "1 - Supervision or setup",
      GG_Answer: "5 - Setup or clean-up assistance"
    },
    {
      M_Item: "M1840",
      GG_item: "GG0170.D",
      isDiscrepant: true,
      notes: "M=3, GG=6 - Toileting transfer - Expected GG in [2,3,4] but got 6",
      M_Question: "Toilet Transfer: Current ability to get on and off a toilet or commode safely",
      GG_Question: "Sit to stand: Current ability to come to a standing position from sitting",
      M_Answer: "3 - Moderate assistance",
      GG_Answer: "6 - Independent"
    },
    {
      M_Item: "M1845",
      GG_item: "GG0130.C",
      isDiscrepant: false,
      notes: "M=2, GG=3 - Toileting hygiene - Compliant",
      M_Question: "Toileting Hygiene: Current ability to maintain perineal hygiene safely",
      GG_Question: "Toileting hygiene: Current ability to maintain perineal hygiene",
      M_Answer: "2 - Minimal assistance",
      GG_Answer: "3 - Partial/moderate assistance"
    },
    {
      M_Item: "M1850",
      GG_item: "GG0170.B",
      isDiscrepant: false,
      notes: "M=0, GG=6 - Bed/chair transfers - Compliant",
      M_Question: "Bed Transfer: Current ability to get in and out of bed safely",
      GG_Question: "Sit to lying: Current ability to move from sitting on side of bed to lying flat",
      M_Answer: "0 - Independent",
      GG_Answer: "6 - Independent"
    },
    {
      M_Item: "M1850",
      GG_item: "GG0170.C",
      isDiscrepant: true,
      notes: "M=1, GG=3 - Bed/chair transfers - Expected GG in [5] but got 3",
      M_Question: "Chair Transfer: Current ability to transfer to and from chairs safely",
      GG_Question: "Lying to sitting on side of bed: Current ability to move from lying to sitting",
      M_Answer: "1 - Supervision or setup",
      GG_Answer: "3 - Partial/moderate assistance"
    },
    {
      M_Item: "M1860",
      GG_item: "GG0170.J",
      isDiscrepant: true,
      notes: "M=4, GG=3 - Ambulation/locomotion - Expected GG in [1] but got 3",
      M_Question: "Ambulation/Locomotion: Current ability to walk safely once in a standing position",
      GG_Question: "Walk 10 feet: Current ability to walk at least 10 feet in a room",
      M_Answer: "4 - Maximal assistance",
      GG_Answer: "3 - Partial/moderate assistance"
    },
    {
      M_Item: "M1860",
      GG_item: "GG0170.K",
      isDiscrepant: false,
      notes: "M=1, GG=5 - Ambulation/locomotion - Compliant",
      M_Question: "Ambulation/Locomotion: Current ability to walk safely once in a standing position",
      GG_Question: "Walk 50 feet with two turns: Current ability to walk at least 50 feet and make two turns",
      M_Answer: "1 - Supervision or setup",
      GG_Answer: "5 - Setup or clean-up assistance"
    },
    {
      M_Item: "M1860",
      GG_item: "GG0170.P",
      isDiscrepant: true,
      notes: "M=2, GG=1 - Ambulation/locomotion - Expected GG in [2,3,4] but got 1",
      M_Question: "Ambulation/Locomotion: Current ability to walk safely once in a standing position",
      GG_Question: "Walk 150 feet: Current ability to walk at least 150 feet in a corridor or similar space",
      M_Answer: "2 - Minimal assistance",
      GG_Answer: "1 - Setup or clean-up assistance"
    }
  ]
};

export const ConflictsTab = ({ route }) => {
    const [flaggingFilter, setFlaggingFilter] = useState<'all' | 'discrepant' | 'compliant'>('all');
    const [expandedRules, setExpandedRules] = useState<{[key: string]: boolean}>({});

    // Initialize expanded state for rules with discrepancies
    React.useEffect(() => {
        const initialExpanded: {[key: string]: boolean} = {};
        Object.entries(sampleFlaggingData).forEach(([ruleName, pairs]) => {
            const hasDiscrepancies = pairs.some(p => p.isDiscrepant);
            initialExpanded[ruleName] = hasDiscrepancies;
        });
        setExpandedRules(initialExpanded);
    }, []);

    // Helper functions for flagging data
    const extractMValue = (notes: string): string => {
        const match = notes.match(/M=(\d+)/);
        return match ? match[1] : 'N/A';
    };

    const extractGGValue = (notes: string): string => {
        const match = notes.match(/GG=(\d+)/);
        return match ? match[1] : 'N/A';
    };

    const extractExpectedValues = (notes: string): string => {
        const match = notes.match(/Expected GG in \[([^\]]+)\]/);
        return match ? match[1] : '';
    };

    const getValueDescription = (value: string): string => {
        const descriptions: {[key: string]: string} = {
            '0': 'Independent',
            '1': 'Supervision/Setup',
            '2': 'Minimal Assistance',
            '3': 'Moderate Assistance',
            '4': 'Maximal Assistance',
            '5': 'Dependent',
            '6': 'Independent'
        };
        return descriptions[value] || 'Unknown';
    };

    const toggleRuleExpansion = (ruleName: string) => {
        setExpandedRules(prev => ({
            ...prev,
            [ruleName]: !prev[ruleName]
        }));
    };

    const getFilteredPairs = (pairs: FlaggingPair[]) => {
        if (flaggingFilter === 'discrepant') return pairs.filter(p => p.isDiscrepant);
        if (flaggingFilter === 'compliant') return pairs.filter(p => !p.isDiscrepant);
        return pairs;
    };

    return (
        <View style={styles.container}>
            <ScrollView style={styles.content}>
                {/* Summary Card */}
                {(() => {
                    const allRules = Object.entries(sampleFlaggingData);
                    const totalPairs = allRules.reduce((sum, [, pairs]) => sum + pairs.length, 0);
                    const totalDiscrepant = allRules.reduce((sum, [, pairs]) => sum + pairs.filter(p => p.isDiscrepant).length, 0);
                    const totalCompliant = totalPairs - totalDiscrepant;

                    return (
                        <View style={[styles.summaryCard, { backgroundColor: totalDiscrepant > 0 ? '#FFEBEE' : '#E8F5E8' }]}>
                            <Text style={styles.summaryTitle}>M-GG Consistency Analysis</Text>
                            <View style={styles.summaryRow}>
                                <View style={styles.summaryItem}>
                                    <Text style={styles.summaryNumber}>{allRules.length}</Text>
                                    <Text style={styles.summaryLabel}>Rules Analyzed</Text>
                                </View>
                                <View style={styles.summaryItem}>
                                    <Text style={[styles.summaryNumber, { color: totalDiscrepant > 0 ? '#F44336' : '#4CAF50' }]}>
                                        {totalDiscrepant}
                                    </Text>
                                    <Text style={styles.summaryLabel}>Discrepancies</Text>
                                </View>
                                <View style={styles.summaryItem}>
                                    <Text style={[styles.summaryNumber, { color: '#4CAF50' }]}>{totalCompliant}</Text>
                                    <Text style={styles.summaryLabel}>Compliant</Text>
                                </View>
                            </View>
                        </View>
                    );
                })()}

                {/* Filter Controls */}
                <View style={styles.filterContainer}>
                    <Text style={styles.filterLabel}>Filter:</Text>
                    <View style={styles.filterButtons}>
                        {(['all', 'discrepant', 'compliant'] as const).map((filter) => (
                            <TouchableOpacity
                                key={filter}
                                style={[
                                    styles.filterButton,
                                    flaggingFilter === filter && styles.filterButtonActive
                                ]}
                                onPress={() => setFlaggingFilter(filter)}
                            >
                                <Text style={[
                                    styles.filterButtonText,
                                    flaggingFilter === filter && styles.filterButtonTextActive
                                ]}>
                                    {filter === 'all' ? 'All Pairs' : filter === 'discrepant' ? 'Discrepancy Only' : 'Compliant Only'}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>

                {/* Rules Sections */}
                {Object.entries(sampleFlaggingData).map(([ruleName, pairs]) => {
                    const filteredPairs = getFilteredPairs(pairs);
                    const discrepantCount = pairs.filter(p => p.isDiscrepant).length;
                    const compliantCount = pairs.length - discrepantCount;
                    const hasDiscrepancies = discrepantCount > 0;
                    const isExpanded = expandedRules[ruleName] ?? hasDiscrepancies;

                    if (filteredPairs.length === 0) return null;

                    return (
                        <View key={ruleName} style={styles.ruleSection}>
                            <TouchableOpacity
                                style={styles.ruleHeader}
                                onPress={() => toggleRuleExpansion(ruleName)}
                            >
                                <View style={styles.ruleHeaderContent}>
                                    <Text style={styles.ruleIcon}>{hasDiscrepancies ? '🔴' : '🟢'}</Text>
                                    <Text style={styles.ruleTitle}>
                                        {"SOC RN"}
                                    </Text>
                                </View>
                                <View style={styles.ruleHeaderRight}>
                                    <View style={[styles.ruleChip, { backgroundColor: hasDiscrepancies ? '#FFCDD2' : '#C8E6C9' }]}>
                                        <Text style={[styles.ruleChipText, { color: hasDiscrepancies ? '#D32F2F' : '#388E3C' }]}>
                                            {discrepantCount} discrepant, {compliantCount} compliant
                                        </Text>
                                    </View>
                                    <MaterialIcons
                                        name={isExpanded ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                                        size={24}
                                        color="#666"
                                    />
                                </View>
                            </TouchableOpacity>

                            {isExpanded && (
                                <View style={styles.pairsContainer}>
                                    {filteredPairs.map((pair, index) => {
                                        const mValue = extractMValue(pair.notes);
                                        const ggValue = extractGGValue(pair.notes);
                                        const expectedValues = extractExpectedValues(pair.notes);

                                        return (
                                            <View
                                                key={`${pair.M_Item}-${pair.GG_item}-${index}`}
                                                style={[
                                                    styles.pairCard,
                                                    { backgroundColor: pair.isDiscrepant ? '#FFEBEE' : '#E8F5E8' }
                                                ]}
                                            >
                                                <View style={styles.pairHeader}>
                                                    <Text style={styles.pairStatus}>
                                                        {pair.isDiscrepant ? '❌' : '✅'}
                                                    </Text>
                                                    <View style={[
                                                        styles.statusChip,
                                                        { backgroundColor: pair.isDiscrepant ? '#FFCDD2' : '#C8E6C9' }
                                                    ]}>
                                                        <Text style={[
                                                            styles.statusChipText,
                                                            { color: pair.isDiscrepant ? '#D32F2F' : '#388E3C' }
                                                        ]}>
                                                            {pair.isDiscrepant ? 'Discrepancy' : 'Compliant'}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={styles.pairContent}>
                                                    <View style={styles.itemSection}>
                                                        <Text style={styles.itemCode}>{pair.M_Item}</Text>
                                                        <Text style={styles.itemDescription}>
                                                            {pair.M_Question || "M-Item Assessment"}
                                                        </Text>
                                                        <View style={styles.valueContainer}>
                                                            <Text style={styles.valueLabel}>Answer: </Text>
                                                            <Text style={styles.valueText}>
                                                                {pair.M_Answer || `${mValue} - ${getValueDescription(mValue)}`}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <View style={styles.itemSection}>
                                                        <Text style={styles.itemCode}>{pair.GG_item}</Text>
                                                        <Text style={styles.itemDescription}>
                                                            {pair.GG_Question || "GG-Item Assessment"}
                                                        </Text>
                                                        <View style={styles.valueContainer}>
                                                            <Text style={styles.valueLabel}>Answer: </Text>
                                                            <Text style={styles.valueText}>
                                                                {pair.GG_Answer || `${ggValue} - ${getValueDescription(ggValue)}`}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    {pair.isDiscrepant && expectedValues && (
                                                        <View style={styles.expectedSection}>
                                                            <Text style={styles.expectedLabel}>Expected GG Values: </Text>
                                                            <Text style={styles.expectedText}>{expectedValues}</Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        );
                                    })}
                                </View>
                            )}
                        </View>
                    );
                })}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    content: {
        flex: 1,
        padding: 16,
    },
    summaryCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    summaryTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 12,
        textAlign: 'center',
        color: '#333',
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    summaryItem: {
        alignItems: 'center',
    },
    summaryNumber: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
    },
    summaryLabel: {
        fontSize: 12,
        color: '#666',
        marginTop: 4,
    },
    filterContainer: {
        marginBottom: 16,
    },
    filterLabel: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
        color: '#333',
    },
    filterButtons: {
        flexDirection: 'row',
        gap: 8,
    },
    filterButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ddd',
    },
    filterButtonActive: {
        backgroundColor: '#1D73D6',
        borderColor: '#1D73D6',
    },
    filterButtonText: {
        fontSize: 14,
        color: '#666',
    },
    filterButtonTextActive: {
        color: '#fff',
        fontWeight: '500',
    },
    ruleSection: {
        marginBottom: 16,
        backgroundColor: '#fff',
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    ruleHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
    },
    ruleHeaderContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    ruleIcon: {
        fontSize: 16,
        marginRight: 8,
    },
    ruleTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },
    ruleHeaderRight: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    ruleChip: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    ruleChipText: {
        fontSize: 12,
        fontWeight: '500',
    },
    pairsContainer: {
        paddingHorizontal: 16,
        paddingBottom: 16,
    },
    pairCard: {
        marginBottom: 12,
        borderRadius: 8,
        padding: 12,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    pairHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        gap: 8,
    },
    pairStatus: {
        fontSize: 16,
    },
    statusChip: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusChipText: {
        fontSize: 12,
        fontWeight: '500',
    },
    pairContent: {
        gap: 12,
    },
    itemSection: {
        backgroundColor: '#f9f9f9',
        padding: 12,
        borderRadius: 8,
    },
    itemCode: {
        fontSize: 14,
        fontWeight: '600',
        color: '#1D73D6',
        marginBottom: 4,
    },
    itemDescription: {
        fontSize: 14,
        color: '#333',
        marginBottom: 8,
        lineHeight: 20,
    },
    valueContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    valueLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666',
    },
    valueText: {
        fontSize: 14,
        color: '#333',
        flex: 1,
    },
    expectedSection: {
        backgroundColor: '#fff3cd',
        padding: 8,
        borderRadius: 6,
        borderLeftWidth: 4,
        borderLeftColor: '#ffc107',
    },
    expectedLabel: {
        fontSize: 12,
        fontWeight: '600',
        color: '#856404',
    },
    expectedText: {
        fontSize: 12,
        color: '#856404',
    },
});
