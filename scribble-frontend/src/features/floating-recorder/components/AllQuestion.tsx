import React, { useEffect, useState } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Alert,
    ActivityIndicator,
    Modal,
    Dimensions,
    Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AntDesign from '@expo/vector-icons/AntDesign';

import { fetchFormDetails, updateAssesment } from '../record-api';
import { useNavigation } from '@react-navigation/native';
import RecursiveItemRenderer from '../../form-builder/components/RecursiveItemRenderer';
import { FormLayoutComponentChildrenType } from 'src/features/form-builder/types/FormTemplateTypes';
import theme from '@/src/theme';
import { FormControlNames } from '../../form-builder/utils/FormBuilderUtils';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { applySkipLogic } from '../../form-builder/utils/SkipLogicUtils';
import { DynamicMessageModal, DynamicMessageModalWithConfirm, MessageModal } from '@/src/utils/MessageModal';
import { ASSESMENT_FAILED, ASSESMENT_SUBMISSION_MESSAGE, ASSESMENT_SUBMISSION_TITLE, ASSESMENT_SUCCESS } from '@/src/components/messages';
import { set } from 'date-fns';
import { status } from '@/src/constants/status';

const { width, height } = Dimensions.get('window');



const SubmitConfirmModal = ({ visible, onCancel, onConfirm }) => {
    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={visible}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContainer}>
                    {/* Checkmark Icon */}
                    <View style={styles.iconContainer}>
                        <View style={styles.icon}>
                            <AntDesign name="exclamationcircle" size={30} color="#1D75F5" />
                        </View>
                    </View>

                    {/* Title */}
                    <Text style={styles.title}>{ASSESMENT_SUBMISSION_TITLE}</Text>

                    {/* Message */}
                    <Text style={styles.message}>
                        {ASSESMENT_SUBMISSION_MESSAGE}
                        {/* Are you sure, you'd like to submit the form to the EMR? */}
                    </Text>

                    {/* Buttons */}
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
                            <Text style={styles.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
                            <Text style={styles.confirmButtonText}>Confirm</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export const AllQuestion = ({ route }) => {
    const assesId = route.params.visitId;
    const navigation = useNavigation();
    setPageTitle('Review Assessment');
    const [categories, setCategories] = useState([{
        container: { id: '', heading: '', subHeading: '' },
        items: []
    }]);

    const [totalQuestions, setTotalQuestions] = useState(0);
    const [answeredQuestion, setAnsweredQuestion] = useState(0);
    const [progress, setProgress] = useState(0);
    const [loading, setLoading] = useState(true);
    const [isOpenSubmitModal, setIsOpenSubmitModal] = useState(false);

    const [showMessageModal, setShowMessageModal] = useState(false)
    const [assesmentId, setAssesmentId] = useState(0);
    const [isOpenMessageModal,setIsOpenMessageModal   ] = useState(false)
    const [startingLayout, setStartingLayout] = useState([{
        container: { id: '', heading: '', subHeading: '' },
        items: []
    }])
    const [isPageLoadingComplete, setIsPageLoadingComplete] = useState(false)
    const [greyedOutQuestions, setGreyedOutQuestions] = useState(new Set());
    const [skipLogicAnswers, setSkipLogicAnswers] = useState({});
    const [isDisabled,setIsDisabled]=useState(true)
    // Function to apply skip logic on page load for questions that already have answers
    const applySkipLogicOnLoad = (categoriesData: any[]) => {
        const newGreyedOutQuestions = new Set();
        const newSkipLogicAnswers = {};

        categoriesData.forEach(category => {
            category.items.forEach(item => {
                if (item.skipLogic && item.answer_text && item.answer_text.length > 0) {
                    // Determine the selected value based on the answer
                    let selectedValue = "";
                    if (item.controlName === FormControlNames.CHECKLIST && item.items) {
                        // Find the option that matches the answer
                        const selectedOption = item.items.find(option =>
                            option.label === item.answer_text[0]
                        );
                        selectedValue = selectedOption ? selectedOption.value : "";
                    } else if (item.controlName === FormControlNames.RADIOGROUP && item.items) {
                        // Find the option that matches the answer
                        const selectedOption = item.items.find(option =>
                            option.label === item.answer_text[0]
                        );
                        selectedValue = selectedOption ? selectedOption.value : "";
                    } else {
                        // For other question types, use the answer directly
                        selectedValue = item.answer_text[0];
                    }

                    if (selectedValue && item.skipLogic[selectedValue]) {
                        // Apply skip logic
                        const currentState = {
                            hiddenQuestions: newGreyedOutQuestions,
                            skipLogicAnswers: newSkipLogicAnswers
                        };

                        const newState = applySkipLogic(item.questionCode, selectedValue, categoriesData, currentState);

                        // Update the sets
                        newState.hiddenQuestions.forEach(code => newGreyedOutQuestions.add(code));
                        Object.assign(newSkipLogicAnswers, newState.skipLogicAnswers);
                    }
                }
            });
        });

        setGreyedOutQuestions(newGreyedOutQuestions);
        setSkipLogicAnswers(newSkipLogicAnswers);
    };

    const fetchAssesment = () => {
        fetchFormDetails(assesId)
            .then((result) => {
                if (result.status == "ok") {
                    let assesQues = result.data.question;
                    const assesStatus= result.data.status;
                    // console.log("Assesment Status :",status)
                    if(assesStatus === status.READY_FOR_REVIEW || assesStatus === status.AI_PROCESSING_FAILED_){
                        console.log("Ready for review")
                        setIsDisabled(false);
                    }
                    assesQues.forEach(category => {
                        category.items.forEach(item => {
                            if (!item.isContainer && item.controlName === FormControlNames.SCORECALCULATOR /*&& item.isDisplayCheatSheet*/) {
                                // If we have a saved calculated score, use it
                                if (item.answer_text && item.answer_text.length > 0 && item.answer_text[0] !== 'Not Available') {
                                    const savedScore = parseFloat(item.answer_text[0]);
                                    if (!isNaN(savedScore)) {
                                        item.calculatedScore = savedScore;
                                    }
                                }
                            }
                        });
                    });

                    let totalCount = 0;
                    let answerCount = 0;

                    // Count total questions and answered questions (including score calculators)
                    for (let i = 0; i < assesQues.length; i++) {
                        for (let j = 0; j < assesQues[i].items.length; j++) {
                            if (!assesQues[i].items[j].isContainer  /* &&  assesQues[i].items[j].isDisplayCheatSheet*/) {
                                totalCount++;

                                if (assesQues[i].items[j].answer_text && Array.isArray(assesQues[i].items[j].answer_text) && assesQues[i].items[j].answer_text.length > 0) {
                                    if (assesQues[i].items[j].controlName == FormControlNames.CHECKBOX || assesQues[i].items[j].controlName == FormControlNames.TOGGLE) {
                                        if (assesQues[i].items[j].answer_text[0] === 'Yes' || assesQues[i].items[j].answer_text[0] === 'true') {
                                            answerCount++;
                                        }
                                    } else if (assesQues[i].items[j].controlName == FormControlNames.SCORECALCULATOR) {
                                        // Score calculators are considered answered if they have a calculated score
                                        if (assesQues[i].items[j].calculatedScore !== undefined ||
                                            (assesQues[i].items[j].answer_text[0] !== "Not Available" && assesQues[i].items[j].answer_text[0] !== "0")) {
                                            answerCount++;
                                        }
                                    } else {
                                        if (assesQues[i].items[j].answer_text[0] != "Not Available") {
                                            answerCount++;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    setTotalQuestions(totalCount);
                    const percentage = (answerCount / totalCount) * 100;
                    setProgress(percentage);
                    setAnsweredQuestion(answerCount);
                    setStartingLayout(JSON.parse(JSON.stringify(assesQues)));
                    setCategories(assesQues);

                    // Apply skip logic for questions that already have answers
                    applySkipLogicOnLoad(assesQues);

                    setIsPageLoadingComplete(true);
                } else {
                    Alert.alert('Something went wrong. Please try again later.');
                }
            })
            .catch((error: any) => {
                console.log(error);
                Alert.alert(error, 'Something went wrong. Please try again later.');
            })
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        setAssesmentId(assesId);
        fetchAssesment();
    }, []);

    /**
     * Update the assesment form after 30 seconds if any changes occurs 
     * in the form
     */
    useEffect(() => {
        if(!isDisabled){
        let interval = setInterval(() => {
            if (isPageLoadingComplete) {
                if (JSON.stringify(startingLayout) != JSON.stringify(categories)) {
                    processScoreCalculators();
                    let data: any = { question: categories };
                    updateAssesmentDetails(data).then((result: any) => {
                        if (result.status == "ok") {
                            setStartingLayout(JSON.parse(JSON.stringify(categories)))
                        } else {
                        }
                    })
                }
            }
        }, 30000);
        return () => clearInterval(interval);
    }
    });

    const processScoreCalculators = () => {
        const updatedCategories = [...categories];
        let hasUpdates = false;

        updatedCategories.forEach(category => {
            category.items.forEach(item => {
                if (!item.isContainer && item.controlName === FormControlNames.SCORECALCULATOR /* && item.isDisplayCheatSheet */) {
                    // Ensure score calculators have their calculated values in answer_text
                    if (item.calculatedScore !== undefined) {
                        const formattedScore = formatScoreForSaving(item);
                        if (!item.answer_text || item.answer_text[0] !== formattedScore) {
                            item.answer_text = [formattedScore];
                            hasUpdates = true;
                        }
                    }
                }
            });
        });

        if (hasUpdates) {
            setCategories(updatedCategories);
        }
    };

    const formatScoreForSaving = (item: FormLayoutComponentChildrenType): string => {
        if (!item.calculatedScore) return '0';

        if (item.displayFormat) {
            const decimalPlaces = parseInt(item.displayFormat.replace('.', '')) || 0;
            return item.calculatedScore.toFixed(decimalPlaces);
        }

        if (item.calculationType === 'average') {
            return item.calculatedScore.toFixed(2);
        }

        return item.calculatedScore.toString();
    };

    const getAllAnswers = (): Record<string, string[]> => {
        const allAnswers: Record<string, string[]> = {};

        categories.forEach(category => {
            category.items.forEach(item => {
                if (!item.isContainer && item.answer_text && Array.isArray(item.answer_text) /* && item.isDisplayCheatSheet */) {
                    allAnswers[item.id] = item.answer_text;
                }
            });
        });

        return allAnswers;
    };
    // Function to update item in the categories state
    const handleItemChange = (updatedItem: FormLayoutComponentChildrenType) => {
        const newCategories = [...categories];

        // Find the category and item to update
        for (let i = 0; i < newCategories.length; i++) {
            const category = newCategories[i];

            // Find the item in this category
            const itemIndex = category.items.findIndex(item => item.id === updatedItem.id);

            if (itemIndex !== -1) {
                // Update the item
                category.items[itemIndex] = updatedItem;

                // Apply skip logic if the question has skip logic configuration
                if (updatedItem.skipLogic) {
                    // Determine the selected value based on the answer
                    let selectedValue = "";
                    if (updatedItem.answer_text && updatedItem.answer_text.length > 0) {
                        // For checklist questions, we need to map the answer to the option value
                        if (updatedItem.controlName === FormControlNames.CHECKLIST && updatedItem.items) {
                            // Find the option that matches the answer
                            const selectedOption = updatedItem.items.find(option =>
                                option.label === updatedItem.answer_text[0]
                            );
                            selectedValue = selectedOption ? selectedOption.value : "";
                        } else if (updatedItem.controlName === FormControlNames.RADIOGROUP && updatedItem.items) {
                            // Find the option that matches the answer
                            const selectedOption = updatedItem.items.find(option =>
                                option.label === updatedItem.answer_text[0]
                            );
                            selectedValue = selectedOption ? selectedOption.value : "";
                        } else {
                            // For other question types, use the answer directly
                            selectedValue = updatedItem.answer_text[0];
                        }
                    }

                    // Apply skip logic
                    const currentState = {
                        hiddenQuestions: greyedOutQuestions,
                        skipLogicAnswers
                    };

                    const newState = applySkipLogic(updatedItem.questionCode, selectedValue, newCategories, currentState);
                    setGreyedOutQuestions(newState.hiddenQuestions);
                    setSkipLogicAnswers(newState.skipLogicAnswers);
                }

                break;
            }
        }

        setCategories(newCategories);
    };

    // Calculate progress whenever categories state changes
    const calculateProgress = () => {
        let totalCount = 0;
        let answerCount = 0;

        for (let i = 0; i < categories.length; i++) {
            for (let j = 0; j < categories[i].items.length; j++) {
                if (!categories[i].items[j].isContainer) {
                    totalCount++;
                    if (categories[i].items[j].answer_text && Array.isArray(categories[i].items[j].answer_text) && categories[i].items[j].answer_text.length > 0 /*&& categories[i].items[j].isDisplayCheatSheet*/) {
                        if (categories[i].items[j].controlName == FormControlNames.CHECKBOX || categories[i].items[j].controlName == FormControlNames.TOGGLE) {
                            if (categories[i].items[j].answer_text[0] === 'Yes' || categories[i].items[j].answer_text[0] === 'true') {
                                answerCount++;
                            }
                        } else {
                            if (categories[i].items[j].answer_text[0] != "Not Available") {
                                answerCount++;
                            }
                        }
                    }
                }
            }
        }

        setTotalQuestions(totalCount);
        setAnsweredQuestion(answerCount);

        if (totalCount === 0) {
            setProgress(0);
        } else {
            setProgress((answerCount / totalCount) * 100);
        }
    };

    const handleSaveDraft = () => {
        processScoreCalculators();
        let data: any = { question: categories };
        setLoading(true);
        updateAssesmentDetails(data).then((result: any) => {
            setLoading(false);
            if (result.status === "ok") {
                setStartingLayout(JSON.parse(JSON.stringify(categories)));
                // Show success message or navigate
            }
        }, (err) => {
            setShowMessageModal(true);
            setLoading(false);
        });
    };

    const handleSubmitWithFlagging = () => {
        setIsOpenSubmitModal(true);
    };

    const submitEvent = (type: string) => {
        setIsOpenSubmitModal(false);
        
        processScoreCalculators();

        let data: any = { question: categories };
        if (type === "submit") {
            data["status"] = "Submitted to EMR";
        }
        setLoading(true)
        // setIsOpenMessageModal(true)
        updateAssesmentDetails(data).then((result: any) => {
            setLoading(false);
            // console.log("Response: ",JSON.stringify(result));
            if (result.status === "ok") {
                console.log("Inside Success")
                setIsOpenMessageModal(true)
                // navigation.navigate('Dashboard');
            } else {
            }
        }, (err) => {
            console.log("Inside Error")
                setShowMessageModal(true);
            setLoading(false)
        })
    };

    const updateAssesmentDetails = (data: any) => {
        return new Promise((resolve, reject) => {
            updateAssesment(data, assesmentId)
                .then((result) => {
                    resolve(result)
                })
                .catch((error) => {
                    console.log(error);
                    reject()
                })
        })
    }

    const onModalCancel = () => {
        setIsOpenSubmitModal(false);
    };

    const openSubmitModal = () => {
        setIsOpenSubmitModal(true);
    };

    const renderWebContent = () => {
        return (
            <div
                style={{
                    overflowY: 'auto',
                    height: height * 0.60,
                    paddingBottom: 80, // Add padding to account for footer
                }}
            >
                {categories.map((category) => (
                    <View key={category.container.id} style={styles.questionItem}>
                        <View style={styles.sectionTitleContainer}>
                            <Text style={[
                                styles.sectionTitle,
                                // Check if any question in this category is greyed out
                                category.items.some(item => greyedOutQuestions.has(item.questionCode)) ? styles.greyedOutText : null
                            ]}>{category.container.subHeading}</Text>
                            <View style={styles.line} />
                        </View>
                        {category.items.map((item) => {
                            if (item.isDisplayCheatSheet) {
                                const isGreyedOut = greyedOutQuestions.has(item.questionCode) ;
                                return (

                                    <RecursiveItemRenderer
                                        key={item.id}
                                        item={item}
                                        level={item.level || 0}
                                        onItemChange={handleItemChange}
                                        onAnswerUpdate={calculateProgress}
                                        allAnswers={getAllAnswers()} // Add this line
                                        containerItems={category.items} // Add this line
                                        isGreyedOut={isGreyedOut}
                                        isDisabledFields={isDisabled} 
                                    />
                                )
                            }
                        }
                        )}
                    </View>
                ))}
            </div>
        );
    };

    const renderNativeContent = () => {
        return (
            <ScrollView
                style={styles.questionsContainer}
                contentContainerStyle={{ paddingBottom: 80 }} // Add padding for footer
            >
                {categories.map((category) => (
                    <View key={category.container.id} style={styles.questionItem}>
                        <View style={styles.sectionTitleContainer}>
                            <Text style={[
                                styles.sectionTitle,
                                // Check if any question in this category is greyed out
                                category.items.some(item => greyedOutQuestions.has(item.questionCode)) ? styles.greyedOutText : null
                            ]}>{category.container.subHeading}</Text>
                            <View style={styles.line} />
                        </View>
                        {category.items.map((item) => {
                            const isGreyedOut = greyedOutQuestions.has(item.questionCode) ;
                            return (
                                <RecursiveItemRenderer
                                    key={item.id}
                                    item={item}
                                    level={item.level || 0}
                                    onItemChange={handleItemChange}
                                    onAnswerUpdate={calculateProgress}
                                    allAnswers={getAllAnswers()}
                                    containerItems={category.items}
                                    isGreyedOut={isGreyedOut}
                                    isDisabledFields={isDisabled} 
                                />
                            );
                        })}

                    </View>
                ))}
            </ScrollView>
        );
    };
const onCancelSuccessModal=()=>{
    setIsOpenMessageModal(false);
    navigation.navigate('Dashboard');
}
    return (
        <View style={styles.container}>
            <SubmitConfirmModal
                visible={isOpenSubmitModal}
                onCancel={onModalCancel}
                onConfirm={() => submitEvent('submit')}
            />
            <MessageModal visible={showMessageModal} onCancel={()=>setShowMessageModal(false)} message={ASSESMENT_FAILED} type="error" />
            {/* <DynamicMessageModalWithConfirm
             visible={isOpenSubmitModal} 
             onCancel={()=>{onModalCancel}}
             title={ASSESMENT_SUBMISSION_TITLE}
              message={ASSESMENT_SUBMISSION_MESSAGE} 
              onConfirm={submitEvent('submit')}
              iconComponent={<Ionicons name="checkmark-circle" size={35} color="#1D75F5" />} /> */}
            
            <DynamicMessageModalWithConfirm
             visible={isOpenMessageModal} 
             onCancel={()=>{onCancelSuccessModal()}}
              message={ASSESMENT_SUCCESS} 
              iconComponent={<Ionicons name="checkmark-circle" size={35} color="#1D75F5" />} cancelButtonText='OK' />

            {loading ? (
                <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 10 }} />
            ) : categories.length > 0 ? (
                <>
                    {/* Section progress */}
                    <View style={styles.progressContainer}>
                        <Text style={styles.sectionText}>Questions</Text>
                        <Text style={styles.sectionText}>{answeredQuestion}/{totalQuestions}</Text>
                    </View>

                    <View style={styles.progressBarContainer}>
                        <View style={[styles.progressBar, { width: `${progress}%` }]} />
                    </View>

                    {/* Questions list - in container with flex: 1 to take remaining space */}
                    <View style={{ flex: 1 }}>
                        {Platform.OS === 'web' ? renderWebContent() : renderNativeContent()}
                    </View>

                    {/* Footer buttons - positioned absolutely at bottom */}
                    {!isDisabled && 
                    <View style={styles.footer}>
                        <TouchableOpacity
                            style={styles.saveButton}
                            onPress={handleSaveDraft}
                        >
                            <Text style={styles.saveButtonText}>Save</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.submitButton}
                            onPress={handleSubmitWithFlagging}
                        >
                            <Text style={styles.submitButtonText}>Submit</Text>
                        </TouchableOpacity>
                    </View>
}
                </>
            ) : (
                <View style={styles.blankTextContainer}>
                    <Text style={styles.blankText}>No Question available</Text>
                </View>
            )}


        </View>
    );
};

const styles = StyleSheet.create({
    icon: {
        width: 60,
        height: 60,
        backgroundColor: '#EFF6FF',
        borderRadius: 30,
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
    },
    container: {
        flex: 1,
        backgroundColor: '#F3F5F7',
    },
    blankTextContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center"
    },
    blankText: {
        textAlign: "center",
        marginTop: 20,
        fontSize: 20,
        fontFamily: "Poppins_500Medium",
    },
    header: {
        backgroundColor: '#fff',
        paddingTop: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#e1e1e1',
    },
    line: {
        borderBottomWidth: 1,
        borderBottomColor: '#E3E3E3',
        marginTop: 10
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 10,
        paddingHorizontal: 15,
    },
    backText: {
        fontSize: 20,
        fontFamily: "Poppins_500Medium",
        color: '#000',
        marginLeft: 5,
    },
    tabContainer: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#e1e1e1',
    },
    tab: {
        flex: 1,
        paddingVertical: 15,
        alignItems: 'center',
    },
    activeTab: {
        borderBottomWidth: 2,
        borderBottomColor: '#1D73D6',
    },
    tabText: {
        fontSize: 14,
        color: '#7C7887',
        fontFamily: "Poppins_400Regular",
    },
    activeTabText: {
        color: '#1D73D6',
        fontFamily: "Poppins_400Regular",
        fontSize: 14,
    },
    progressContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingVertical: 10,
        backgroundColor: '#F3F5F7',
        marginTop: 10,
    },
    sectionText: {
        fontSize: 14,
        color: '#7C7887',
        fontFamily: "Poppins_400Regular",
    },
    progressBarContainer: {
        height: 8,
        backgroundColor: '#EAEAEA',
        marginLeft: 15,
        marginRight: 15,
        borderRadius: 10,
        marginBottom: 15,
    },
    progressBar: {
        height: 8,
        backgroundColor: '#0079FE',
        borderRadius: 10,
    },
    sectionTitleContainer: {
        backgroundColor: '#F3F5F7',
        paddingVertical: 0,
        marginTop: 1,
    },
    sectionTitle: {
        fontSize: 18,
        fontFamily: 'Poppins_600SemiBold',
        color: '#000000',
    },
    greyedOutText: {
        color: '#9CA3AF',
        opacity: 0.6,
    },
    questionsContainer: {
        flex: 1,
        //paddingStart:10,
       // paddingEnd:20
    },
    questionItem: {
        backgroundColor: '#F3F5F7',
        paddingHorizontal: 15,
        paddingVertical: 0,
    },
    questionCode: {
        fontSize: 12,
        color: '#7C7887',
        marginBottom: 5,
        fontFamily: "Poppins_500Medium",
    },
    questionText: {
        fontSize: 14,
        marginBottom: 15,
        lineHeight: 22,
        color: '#251F38',
        fontFamily: 'Poppins_400Regular',
    },
    dropdown: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        padding: 12,
        backgroundColor: '#fff',
    },
    dropdownText: {
        fontSize: 16,
        color: '#333',
    },
    dropdownOptions: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        marginTop: 5,
        backgroundColor: '#fff',
    },
    optionItem: {
        padding: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    optionText: {
        fontSize: 16,
    },
    selectedOptionText: {
        fontWeight: '500',
        color: '#007AFF',
    },
    errorText: {
        color: '#FF3B30',
        marginTop: 8,
        fontSize: 14,
    },
    footer: {
        flexDirection: 'row',
        padding: 15,
        backgroundColor: '#F3F5F7',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        borderTopWidth: 1,
        borderTopColor: '#E2E8F0',
        zIndex: 1000,
    },
    saveButton: {
        flex: 1,
        borderWidth: 1,
        borderColor: theme.colors.buttonColor,
        borderRadius: 30,
        padding: 12,
        alignItems: 'center',
        marginRight: 10,
    },
    saveButtonText: {
        color: theme.colors.buttonColor,
        fontSize: 14,
        fontFamily: "Poppins_500Medium",
    },
    submitButton: {
        flex: 1,
        backgroundColor: theme.colors.buttonColor,
        borderRadius: 30,
        padding: 12,
        alignItems: 'center',
    },
    submitButtonText: {
        color: '#fff',
        fontSize: 14,
        fontFamily: "Poppins_500Medium",
    },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        width: width * 0.95,
        backgroundColor: '#FFF',
        paddingVertical: 24,
        paddingHorizontal: 20,
        borderRadius: 16,
        alignItems: 'center',
        elevation: 5,
    },
    iconContainer: {
        marginBottom: 25,
    },
    title: {
        color: '#000',
        textAlign: 'center',
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 24,
        fontStyle: 'normal',
    },
    message: {
        color: '#7C7887',
        textAlign: 'center',
        fontFamily: 'Poppins_400Regular',
        fontSize: 18,
        fontStyle: 'normal',
        fontWeight: '400',
        marginTop: 10,
        marginBottom: 15
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
        marginTop: 15
    },
    cancelButton: {
        flex: 1,
        borderWidth: 1,
        borderColor: theme.colors.buttonColor,
        paddingVertical: 10,
        borderRadius: 24,
        marginRight: 8,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: theme.colors.buttonColor,
        fontSize: 16,
        fontFamily: 'Poppins_500Medium',
    },
    confirmButton: {
        flex: 1,
        backgroundColor: theme.colors.buttonColor,
        paddingVertical: 10,
        borderRadius: 24,
        marginLeft: 8,
        alignItems: 'center',
    },
    confirmButtonText: {
        color: '#FFF',
        fontSize: 16,
        fontFamily: 'Poppins_500Medium',
    },
});

export default AllQuestion;