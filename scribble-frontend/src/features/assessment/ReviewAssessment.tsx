import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Modal,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';
import { useNavigation } from '@react-navigation/native';
// import { ChevronLeft } from 'lucide-react-native';

// M-GG Flagging interfaces
interface FlaggingPair {
  M_Item: string;
  GG_item: string;
  isDiscrepant: boolean;
  notes: string;
}

interface FlaggingRule {
  [key: string]: FlaggingPair[];
}

// Hard-coded sample data for POC
const sampleFlaggingData: FlaggingRule = {
  "7_rule_partial_independence": [
    {
      M_Item: "M1810",
      GG_item: "GG0130.A",
      isDiscrepant: false,
      notes: "M=0, GG=6 - Grooming ≈ oral hygiene - Compliant"
    },
    {
      M_Item: "M1810",
      GG_item: "GG0130.B",
      isDiscrepant: false,
      notes: "M=0, GG=6 - Grooming ≈ oral hygiene - Compliant"
    },
    {
      M_Item: "M1820",
      GG_item: "GG0130.C",
      isDiscrepant: true,
      notes: "M=2, GG=1 - Upper dressing - Expected GG in [2,3,4] but got 1"
    },
    {
      M_Item: "M1830",
      GG_item: "GG0130.E",
      isDiscrepant: false,
      notes: "M=1, GG=5 - Bathing - Compliant"
    },
    {
      M_Item: "M1840",
      GG_item: "GG0170.D",
      isDiscrepant: true,
      notes: "M=3, GG=6 - Toileting transfer - Expected GG in [2,3,4] but got 6"
    },
    {
      M_Item: "M1845",
      GG_item: "GG0130.C",
      isDiscrepant: false,
      notes: "M=2, GG=3 - Toileting hygiene - Compliant"
    },
    {
      M_Item: "M1850",
      GG_item: "GG0170.B",
      isDiscrepant: false,
      notes: "M=0, GG=6 - Bed/chair transfers - Compliant"
    },
    {
      M_Item: "M1850",
      GG_item: "GG0170.C",
      isDiscrepant: true,
      notes: "M=1, GG=3 - Bed/chair transfers - Expected GG in [5] but got 3"
    },
    {
      M_Item: "M1860",
      GG_item: "GG0170.J",
      isDiscrepant: true,
      notes: "M=4, GG=3 - Ambulation/locomotion - Expected GG in [1] but got 3"
    },
    {
      M_Item: "M1860",
      GG_item: "GG0170.K",
      isDiscrepant: false,
      notes: "M=1, GG=5 - Ambulation/locomotion - Compliant"
    },
    {
      M_Item: "M1860",
      GG_item: "GG0170.P",
      isDiscrepant: true,
      notes: "M=2, GG=1 - Ambulation/locomotion - Expected GG in [2,3,4] but got 1"
    }
  ]
};

const ReviewAssessment = () => {
  const navigation = useNavigation();

  const [bloodPressure, setBloodPressure] = useState('120/80');
  const [medications, setMedications] = useState(
    'Aspirin 81mg daily\nMetformin 500mg twice daily',
  );
  const [temperature, setTemperature] = useState('');
  const [notes, setNotes] = useState('');
  const [showFlaggingModal, setShowFlaggingModal] = useState(false);
  const [flaggingFilter, setFlaggingFilter] = useState<'all' | 'discrepant' | 'compliant'>('all');
  const [expandedRules, setExpandedRules] = useState<{[key: string]: boolean}>({});

  // Helper functions for flagging data
  const extractMValue = (notes: string): string => {
    const match = notes.match(/M=(\d+)/);
    return match ? match[1] : 'N/A';
  };

  const extractGGValue = (notes: string): string => {
    const match = notes.match(/GG=(\d+)/);
    return match ? match[1] : 'N/A';
  };

  const extractExpectedValues = (notes: string): string => {
    const match = notes.match(/Expected GG in \[([^\]]+)\]/);
    return match ? match[1] : '';
  };

  const getValueDescription = (value: string): string => {
    const descriptions: {[key: string]: string} = {
      '0': 'Independent',
      '1': 'Supervision/Setup',
      '2': 'Minimal Assistance',
      '3': 'Moderate Assistance',
      '4': 'Maximal Assistance',
      '5': 'Dependent',
      '6': 'Independent'
    };
    return descriptions[value] || 'Unknown';
  };

  const handleSaveDraft = () => {
    // Show flagging modal instead of just saving
    setShowFlaggingModal(true);
    // Initialize expanded state for rules with discrepancies
    const initialExpanded: {[key: string]: boolean} = {};
    Object.entries(sampleFlaggingData).forEach(([ruleName, pairs]) => {
      const hasDiscrepancies = pairs.some(p => p.isDiscrepant);
      initialExpanded[ruleName] = hasDiscrepancies;
    });
    setExpandedRules(initialExpanded);
  };

  const toggleRuleExpansion = (ruleName: string) => {
    setExpandedRules(prev => ({
      ...prev,
      [ruleName]: !prev[ruleName]
    }));
  };

  const getFilteredPairs = (pairs: FlaggingPair[]) => {
    if (flaggingFilter === 'discrepant') return pairs.filter(p => p.isDiscrepant);
    if (flaggingFilter === 'compliant') return pairs.filter(p => !p.isDiscrepant);
    return pairs;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <View style={styles.header}>
        <TouchableOpacity
          style={{ position: 'absolute', left: 20, top: 20 }}
          onPress={() => navigation.goBack()}
        >
          {/* <ChevronLeft size={26} /> */}
        </TouchableOpacity>
        <Text style={styles.title}>Review Assessment</Text>
      </View>

      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: '60%' }]} />
        </View>
        <Text style={styles.stepText}>Section 7 of 10</Text>
      </View>
      <View style={{ flex: 1, height: screenHeight - 200 }}>
        <ScrollView style={styles.scrollView}>
          <View style={styles.questionContainer}>
            <Text style={styles.questionLabel}>Question Code: M1001</Text>
            <Text style={styles.questionText}>
              Patient's blood pressure reading?
            </Text>
            <TextInput
              style={styles.input}
              value={bloodPressure}
              onChangeText={setBloodPressure}
              placeholder="Enter blood pressure"
            />
          </View>

          <View style={styles.questionContainer}>
            <Text style={styles.questionLabel}>Question Code: M1001</Text>
            <Text style={styles.questionText}>Current medication list?</Text>
            <TextInput
              style={[styles.input, styles.multilineInput]}
              value={medications}
              onChangeText={setMedications}
              multiline
              numberOfLines={4}
              placeholder="Enter medications"
            />
          </View>

          <View style={styles.missedQuestionsContainer}>
            <Text style={styles.missedQuestionsTitle}>Missed Questions</Text>
            <View style={styles.warningContainer}>
              <MaterialIcons name="warning" size={24} color="#F6A609" />
              <View style={styles.warningContent}>
                <Text style={styles.warningTitle}>
                  Question 3 - Not Answered
                </Text>
                <Text style={styles.warningText}>
                  Patient's temperature reading?
                </Text>
                <TouchableOpacity style={styles.goToButton}>
                  <Text style={styles.goToButtonText}>Go to question</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.notesContainer}>
            <Text style={styles.notesTitle}>Assessment Notes</Text>
            <TextInput
              style={[styles.input, styles.multilineInput]}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              placeholder="Add any notes about your assessment here..."
              placeholderTextColor="#999"
            />
          </View>
        </ScrollView>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.saveButton} onPress={handleSaveDraft}>
          <Text style={styles.saveButtonText}>Save Draft</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.submitButton}>
          <Text style={styles.submitButtonText}>Submit</Text>
        </TouchableOpacity>
      </View>

      {/* M-GG Flagging Modal */}
      <Modal
        visible={showFlaggingModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFlaggingModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>M-GG Consistency Analysis</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowFlaggingModal(false)}
            >
              <MaterialIcons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Summary Card */}
            {(() => {
              const allRules = Object.entries(sampleFlaggingData);
              const totalPairs = allRules.reduce((sum, [, pairs]) => sum + pairs.length, 0);
              const totalDiscrepant = allRules.reduce((sum, [, pairs]) => sum + pairs.filter(p => p.isDiscrepant).length, 0);
              const totalCompliant = totalPairs - totalDiscrepant;

              return (
                <View style={[styles.summaryCard, { backgroundColor: totalDiscrepant > 0 ? '#FFEBEE' : '#E8F5E8' }]}>
                  <Text style={styles.summaryTitle}>Analysis Summary</Text>
                  <View style={styles.summaryRow}>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryNumber}>{allRules.length}</Text>
                      <Text style={styles.summaryLabel}>Rules Analyzed</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryNumber, { color: totalDiscrepant > 0 ? '#F44336' : '#4CAF50' }]}>
                        {totalDiscrepant}
                      </Text>
                      <Text style={styles.summaryLabel}>Discrepancies</Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={[styles.summaryNumber, { color: '#4CAF50' }]}>{totalCompliant}</Text>
                      <Text style={styles.summaryLabel}>Compliant</Text>
                    </View>
                  </View>
                </View>
              );
            })()}

            {/* Filter Controls */}
            <View style={styles.filterContainer}>
              <Text style={styles.filterLabel}>Filter:</Text>
              <View style={styles.filterButtons}>
                {(['all', 'discrepant', 'compliant'] as const).map((filter) => (
                  <TouchableOpacity
                    key={filter}
                    style={[
                      styles.filterButton,
                      flaggingFilter === filter && styles.filterButtonActive
                    ]}
                    onPress={() => setFlaggingFilter(filter)}
                  >
                    <Text style={[
                      styles.filterButtonText,
                      flaggingFilter === filter && styles.filterButtonTextActive
                    ]}>
                      {filter === 'all' ? 'All Pairs' : filter === 'discrepant' ? 'Discrepant Only' : 'Compliant Only'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Rules Sections */}
            {Object.entries(sampleFlaggingData).map(([ruleName, pairs]) => {
              const filteredPairs = getFilteredPairs(pairs);
              const discrepantCount = pairs.filter(p => p.isDiscrepant).length;
              const compliantCount = pairs.length - discrepantCount;
              const hasDiscrepancies = discrepantCount > 0;
              const isExpanded = expandedRules[ruleName] ?? hasDiscrepancies;

              if (filteredPairs.length === 0) return null;

              return (
                <View key={ruleName} style={styles.ruleSection}>
                  <TouchableOpacity
                    style={styles.ruleHeader}
                    onPress={() => toggleRuleExpansion(ruleName)}
                  >
                    <View style={styles.ruleHeaderContent}>
                      <Text style={styles.ruleIcon}>{hasDiscrepancies ? '🔴' : '🟢'}</Text>
                      <Text style={styles.ruleTitle}>
                        {ruleName.replace(/_/g, ' ').replace(/^\d+\s*/, '')}
                      </Text>
                    </View>
                    <View style={styles.ruleHeaderRight}>
                      <View style={[styles.ruleChip, { backgroundColor: hasDiscrepancies ? '#FFCDD2' : '#C8E6C9' }]}>
                        <Text style={[styles.ruleChipText, { color: hasDiscrepancies ? '#D32F2F' : '#388E3C' }]}>
                          {discrepantCount} discrepan, {compliantCount} compliant
                        </Text>
                      </View>
                      <MaterialIcons
                        name={isExpanded ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                        size={24}
                        color="#666"
                      />
                    </View>
                  </TouchableOpacity>

                  {isExpanded && (
                    <View style={styles.pairsContainer}>
                      {filteredPairs.map((pair, index) => {
                        const mValue = extractMValue(pair.notes);
                        const ggValue = extractGGValue(pair.notes);
                        const expectedValues = extractExpectedValues(pair.notes);

                        return (
                          <View
                            key={`${pair.M_Item}-${pair.GG_item}-${index}`}
                            style={[
                              styles.pairCard,
                              { backgroundColor: pair.isDiscrepant ? '#FFEBEE' : '#E8F5E8' }
                            ]}
                          >
                            <View style={styles.pairHeader}>
                              <Text style={styles.pairStatus}>
                                {pair.isDiscrepant ? '❌' : '✅'}
                              </Text>
                              <View style={[
                                styles.statusChip,
                                { backgroundColor: pair.isDiscrepant ? '#FFCDD2' : '#C8E6C9' }
                              ]}>
                                <Text style={[
                                  styles.statusChipText,
                                  { color: pair.isDiscrepant ? '#D32F2F' : '#388E3C' }
                                ]}>
                                  {pair.isDiscrepant ? 'Discrepancy' : 'Compliant'}
                                </Text>
                              </View>
                            </View>

                            <View style={styles.pairContent}>
                              <View style={styles.itemSection}>
                                <Text style={styles.itemCode}>{pair.M_Item}</Text>
                                <Text style={styles.itemDescription}>M-Item Assessment</Text>
                                <View style={styles.valueContainer}>
                                  <Text style={styles.valueLabel}>Value: </Text>
                                  <Text style={styles.valueText}>{mValue} - {getValueDescription(mValue)}</Text>
                                </View>
                              </View>

                              <View style={styles.itemSection}>
                                <Text style={styles.itemCode}>{pair.GG_item}</Text>
                                <Text style={styles.itemDescription}>GG-Item Assessment</Text>
                                <View style={styles.valueContainer}>
                                  <Text style={styles.valueLabel}>Value: </Text>
                                  <Text style={styles.valueText}>{ggValue} - {getValueDescription(ggValue)}</Text>
                                </View>
                              </View>

                              {pair.isDiscrepant && expectedValues && (
                                <View style={styles.expectedSection}>
                                  <Text style={styles.expectedLabel}>Expected GG Values: </Text>
                                  <Text style={styles.expectedText}>{expectedValues}</Text>
                                </View>
                              )}
                            </View>
                          </View>
                        );
                      })}
                    </View>
                  )}
                </View>
              );
            })}
          </ScrollView>

          {/* Modal Footer */}
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.continueButton}
              onPress={() => {
                setShowFlaggingModal(false);
                // Here you could add actual save logic
                console.log('Assessment saved with flagging review completed');
              }}
            >
              <Text style={styles.continueButtonText}>Continue & Save</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 20,
    height: screenHeight - 160,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: 24,
  },
  progressContainer: {
    paddingHorizontal: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4285F4',
    borderRadius: 4,
  },
  stepText: {
    color: '#666',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
  },
  questionContainer: {
    marginBottom: 16,
  },
  questionLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    fontFamily: 'Poppins_500Medium',
  },
  questionText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
    fontFamily: 'Poppins_500Medium',
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  missedQuestionsContainer: {
    marginBottom: 24,
  },
  missedQuestionsTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 16,
  },
  warningContainer: {
    backgroundColor: '#FFF9E6',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  warningContent: {
    marginLeft: 12,
    flex: 1,
  },
  warningTitle: {
    color: '#F6A609',
    fontWeight: '600',
    marginBottom: 4,
  },
  warningText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    marginBottom: 8,
  },
  goToButton: {
    alignSelf: 'flex-start',
  },
  goToButtonText: {
    color: '#F6A609',
    fontWeight: '500',
  },
  notesContainer: {
    marginBottom: 24,
  },
  notesTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 16,
    // flex: 1,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 22,
    height: 74,
  },
  saveButton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: screenWidth / 2 - 24,
  },
  submitButton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#4285F4',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: screenWidth / 2 - 24,
  },
  saveButtonText: {
    color: '#666',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#111827',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 12,
    color: '#111827',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#6366F1',
  },
  summaryLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    marginBottom: 8,
    color: '#374151',
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterButtonActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  filterButtonText: {
    fontSize: 12,
    fontFamily: 'Poppins_500Medium',
    color: '#6B7280',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  ruleSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
  },
  ruleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F9FAFB',
  },
  ruleHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  ruleIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  ruleTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#111827',
    flex: 1,
  },
  ruleHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ruleChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  ruleChipText: {
    fontSize: 10,
    fontFamily: 'Poppins_500Medium',
  },
  pairsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  pairCard: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  pairHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  pairStatus: {
    fontSize: 20,
  },
  statusChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusChipText: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
  },
  pairContent: {
    gap: 12,
  },
  itemSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    padding: 12,
    borderRadius: 6,
  },
  itemCode: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#6366F1',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#6B7280',
    marginBottom: 8,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#374151',
  },
  valueText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#111827',
    flex: 1,
  },
  expectedSection: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    padding: 8,
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#EF4444',
  },
  expectedLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#DC2626',
    marginBottom: 2,
  },
  expectedText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#991B1B',
  },
  modalFooter: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  continueButton: {
    backgroundColor: '#6366F1',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
});

export default ReviewAssessment;
